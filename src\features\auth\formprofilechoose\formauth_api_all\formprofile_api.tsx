
import { customAxios } from "@/utils/axio-interceptor";

export const FormProfileCreate = async (formData: any) => {
  try {
    // Get token for authorization
    const token = localStorage.getItem("access_token") || localStorage.getItem("token");

    console.log("FormProfileCreate - Using token:", token ? "Token exists" : "No token found");
    console.log("FormProfileCreate - Sending data:", formData);

    const response = await customAxios.post(
      "/v1/clients/profile-details",
      formData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    console.log("FormProfileCreate - Success response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("FormProfileCreate - Error details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });

    // Don't let 401 errors clear localStorage for profile creation
    if (error.response?.status === 401) {
      console.error("Profile creation unauthorized - token may be invalid or expired");
      // Re-throw with more specific error message
      throw new Error("Your session has expired. Please login again to continue.");
    }

    // For other errors, throw the original error
    throw error;
  }
};