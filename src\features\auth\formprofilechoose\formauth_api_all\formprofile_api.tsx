
import { customAxios } from "@/utils/axio-interceptor";

export const FormProfileCreate = async (formData: any) => {
  try {
    // Get token for authorization
    const token = localStorage.getItem("access_token") || localStorage.getItem("token");

    const response = await customAxios.post(
      "/v1/clients/profile-details",
      formData,
      {
        headers: {
          Authorization: `Bear<PERSON> ${token}`,
        },
      }
    );
    return response.data;
  } catch (error: any) {
    // Don't let 401 errors clear localStorage for profile creation
    if (error.response?.status === 401) {
      console.error("Profile creation unauthorized - token may be invalid");
      // Re-throw without triggering global logout
      throw new Error("Authentication failed. Please login again.");
    }
    throw error;
  }
};