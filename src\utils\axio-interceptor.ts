import { baseUrl } from "@/globalurl/baseurl";
import axios, { AxiosResponse } from "axios";

// Create axios instance with proper type inference
export const customAxios = axios.create({
  baseURL: baseUrl,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Request interceptor
customAxios.interceptors.request.use(
  (config) => {
    // Add token to headers if available
    const token = localStorage.getItem("access_token") || localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
customAxios.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      console.error("Unauthorized access");

      // Don't clear localStorage if user is on auth profile routes
      const currentPath = window.location.pathname;
      const isAuthProfileRoute = currentPath.includes('/auth/profile') ||
                                currentPath.includes('/auth/plan-select') ||
                                currentPath.includes('/auth/questionaire');

      if (!isAuthProfileRoute) {
        localStorage.clear();
        // Optionally redirect to login page
        // window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);