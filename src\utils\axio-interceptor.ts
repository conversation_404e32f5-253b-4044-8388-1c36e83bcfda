import { baseUrl } from "@/globalurl/baseurl";
import axios, { AxiosResponse } from "axios";

// Create axios instance with proper type inference
export const customAxios = axios.create({
  baseURL: baseUrl,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Request interceptor
customAxios.interceptors.request.use(
  (config) => {
    // Add token to headers if available
    const token = localStorage.getItem("access_token") || localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      // Debug logging for profile creation requests
      if (config.url?.includes('/v1/clients/profile-details')) {
        console.log("Profile creation request - Token found:", token.substring(0, 20) + "...");
      }
    } else {
      // Debug logging when no token is found
      if (config.url?.includes('/v1/clients/profile-details')) {
        console.error("Profile creation request - No token found in localStorage");
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
customAxios.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      console.error("Unauthorized access - Status 401");
      console.error("Request URL:", error.config?.url);
      console.error("Current path:", window.location.pathname);

      // Don't clear localStorage if user is on auth profile routes or signup flow
      const currentPath = window.location.pathname;
      const isAuthProfileRoute = currentPath.includes('/auth/profile') ||
        currentPath.includes('/auth/plan-select') ||
        currentPath.includes('/auth/questionaire') ||
        currentPath.includes('/auth/address-billing');

      // Also check if the failing request is for profile creation
      const isProfileCreationRequest = error.config?.url?.includes('/v1/clients/profile-details');

      if (!isAuthProfileRoute && !isProfileCreationRequest) {
        console.log("Clearing localStorage due to 401 error");
        localStorage.clear();
        // Optionally redirect to login page
        // window.location.href = '/login';
      } else {
        console.log("Skipping localStorage clear - user is in auth profile flow");
      }
    }
    return Promise.reject(error);
  }
);