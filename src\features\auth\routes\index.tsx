import { Route, Routes, Navigate } from "react-router-dom";
import SignupPage from "@/features/auth/routes/signup.page";
import Login from "./login";
import Password from "./password";
import { InputOTPForm } from "./otp";
import ResetPassword from "./updatePassword";
// import { SignupOTPForm } from "./signup-otp";
import AuthRedirect from "@/lib/AuthRedirect";
import ForgetPassword from "./fogetPassword";
import BillingForm from "@/features/clientdashboard/add-on/component/commonpackageplan/address.checkout";
import { AuthCoworker } from "@/features/Coworker/auth/authcoworker";
import AuthQuestionFormClient from "../formprofilechoose/componets/authquestionformclient";
import FormProfileChoose from "../formprofilechoose";
import SignupClientQuestionForm from "../formprofilechoose/signupclientquestionform/signupclientquestionform";
import BankTransfer from "./bank-transfer.page";

const AuthRoutes = () => {
  return (
    <Routes>
      <Route
        index
        element={
          <AuthRedirect>
            <Login />
          </AuthRedirect>
        }
      />
      <Route
        path="/login"
        element={
          <AuthRedirect>
            <Login />
          </AuthRedirect>
        }
      />
      <Route
        path="/signup"
        element={
          <AuthRedirect>
            <SignupPage />
          </AuthRedirect>
        }
      />
      <Route
        path="/password"
        element={
          <AuthRedirect>
            <Password />
          </AuthRedirect>
        }
      />
      {/* <Route
        path="/otpverification"
        element={
          <AuthRedirect>
            <SignupOTPForm />
          </AuthRedirect>
        }
      /> */}
      <Route
        path="/otp-verification"
        element={
          <AuthRedirect>
            <InputOTPForm />
          </AuthRedirect>
        }
      />
      <Route
        path="/questionaire"
        element={
          <AuthRedirect>
            <AuthQuestionFormClient />
          </AuthRedirect>
        }
      />
      <Route
        path="/coworker/accept-invite"
        element={
          <AuthRedirect>
            <AuthCoworker />
          </AuthRedirect>
        }
      />
      <Route
        path="/profile/authprofile"
        element={
          <AuthRedirect skipForPaths={["/profile/authprofile"]}>
            <FormProfileChoose />
          </AuthRedirect>
        }
      />
      <Route
        path="/plan-select"
        element={
          <AuthRedirect>
            <SignupClientQuestionForm />
          </AuthRedirect>
        }
      />
      <Route
        path="/bank-transfer-payment"
        element={
          <AuthRedirect>
            <BankTransfer />
          </AuthRedirect>
        }
      />
      {/* coworker auth  */}
      {/* <Route
        path="/coworker-signup"
        element={
          <AuthRedirect>
            <AuthCoworker />
          </AuthRedirect>
        }
      /> */}
      {/* <Route
        path="/accept-invite"
        element={
          <AuthRedirect>
            <AuthCoworker />
          </AuthRedirect>
        }
      /> */}
      <Route
        path="/address-billing"
        element={
          <AuthRedirect>
            <BillingForm />
          </AuthRedirect>
        }
      />
      
      {/* <Route
        path="/update-password"
        element={
          <AuthRedirect>
            <ResetPassword />
          </AuthRedirect>
        }
      /> */}
      <Route
        path="/update-password"
        element={
          <AuthRedirect skipForPaths={["/update-password"]}>
            <ResetPassword />
          </AuthRedirect>
        }
      />{" "}
      <Route
        path="/forget-password"
        element={
          <AuthRedirect>
            <ForgetPassword />
          </AuthRedirect>
        }
      />
      {/* 👇 Catch-all route for wrong URLs under /auth/* */}
      <Route path="*" element={<Navigate to="/auth/login" replace />} />
    </Routes>
  );
};

export default AuthRoutes;
