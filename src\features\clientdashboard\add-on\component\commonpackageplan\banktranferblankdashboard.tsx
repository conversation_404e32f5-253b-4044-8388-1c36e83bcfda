
import fourcirclegif from "@/assets/icons/circlesfour.gif";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate, useLocation } from "react-router-dom";
import { useEffect, useState } from "react";
import { customAxios } from "@/utils/axio-interceptor";
// import { useDispatch } from "react-redux";
// import { logout } from "@/store/slices/authSlice";
import { Loader2 } from "lucide-react";
import { useAppSelector } from "@/store/hooks/reduxHooks";
import { redirectToDashboard } from "@/utils/navigaterole";

const NewBlankDashboard = () => {
  const navigate = useNavigate();
//   const dispatch = useDispatch();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(true);
  const userRole = useAppSelector((state) => state.auth.user?.role);
  const isAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);

  // Prevent direct URL access - this component is for bantransfer-blank-page (outside dashboard)
  useEffect(() => {
    if (!location.pathname.endsWith("bantransfer-blank-page")) {
      navigate("/bantransfer-blank-page", {
        replace: true,
      });
    }
  }, [location.pathname, navigate]);

  // Handle back button and URL changes for bantransfer-blank-page (outside dashboard)
  useEffect(() => {
    const handleUrlChange = () => {
      if (
        !window.location.pathname.endsWith(
          "bantransfer-blank-page"
        )
      ) {
        window.history.pushState(
          null,
          "",
          "/bantransfer-blank-page"
        );
      }
    };

    window.addEventListener("popstate", handleUrlChange);
    return () => window.removeEventListener("popstate", handleUrlChange);
  }, []);

  // Check annotator count for bank-transfer completed users
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/auth/login", { replace: true });
      return;
    }

    const checkAnnotatorCount = async () => {
      try {
        setIsLoading(true);
        if (userRole === "CLIENT") {
          // For bank-transfer completed users, check if annotators are allocated
          const response = await customAxios.get(
            "/v1/dashboard/client-annotators"
          );
          if (response.data?.count > 0) {
            // Annotators are present - redirect to main dashboard
            redirectToDashboard(userRole, navigate);
          }
          // If count is 0, stay on this page (bank transfer completed but no annotators yet)
        } else if (userRole === "COWORKER") {
          // COWORKER should go to dashboard directly
          navigate("/dashboard", { replace: true });
        }
      } catch (error) {
        console.error("Error checking annotator count:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAnnotatorCount();
  }, [navigate, userRole, isAuthenticated]);

  // const handleContinue = useCallback(() => {
  //   navigate("/dashboard");
  // }, [navigate]);

 
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin" />
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      <div className="flex flex-col gap-4 items-center justify-center flex-grow p-4">
        <img
          src={fourcirclegif}
          alt="Payment successful"
          className="w-[15rem] h-[15rem]"
        />

        <div className="flex flex-row justify-center items-center gap-[0.5rem] text-center">
          <div className="flex flex-col gap-6 mb-6 justify-center items-center w-[80%]">
            <h1 className="text-[28px] font-bold text-[#282828] leading-[100%]">
              Bank transfer completed successfully!
            </h1>
            <p className="text-[#757575] text-[20px] leading-[100%]">
              Your payment has been received. An annotator will be allocated to you shortly. Please wait for at least few hours.
            </p>
          </div>
        </div>

        <div className="flex flex-row justify-center gap-4">
          <Button
            variant={"ghost"}
            className="border px-[18px] py-7 text-[18px] border-[#E91C24] text-[#E91C24]"
            onClick={() => {
              // Try to navigate to dashboard, but user might not have access yet
              // This will be handled by the annotator count check in useEffect
              window.location.href = "/dashboard";
            }}
          >
            Check Dashboard
          </Button>
          <Button
            variant={"gradient"}
            className="px-[40px] py-7 text-[18px]"
            onClick={() => {
              // Open email client for support since this page is outside dashboard
              window.location.href = "mailto:<EMAIL>?subject=Bank Transfer Support Request";
            }}
          >
            Contact Support
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NewBlankDashboard;
