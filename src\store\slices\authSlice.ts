import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { getAvatarUrl } from "../dicebearname/getAvatarUrl"; // Correct path for import

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  passwordHash?: string;
  profilePic?: string;
  userRole?: string;
  availableFrom?: string;
  availableTo?: string;
  coworkerPermission?: string;
}


interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const initialState: AuthState = {
  user: null,
  accessToken: localStorage.getItem("access_token") || localStorage.getItem("token") || null,
  refreshToken: localStorage.getItem("refreshToken") || null,
  isAuthenticated: false,
  isLoading: true,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setUser: (
      state,
      action: PayloadAction<{
        user: User;
        token: string;
      }>
    ) => {
      const { user, token } = action.payload;

      // Generate avatar if not provided (using first name's initials)
      if (!user.profilePic && user.name) {
        user.profilePic = getAvatarUrl(user.name); // Use the helper function
      }

      // Save shift timing for annotator
      if (user.role === "ANNOTATOR" && action.payload.user.availableFrom && action.payload.user.availableTo) {
        user.availableFrom = action.payload.user.availableFrom;
        user.availableTo = action.payload.user.availableTo;
      }

      state.user = user;
      state.accessToken = token;
      state.isAuthenticated = true;
      state.isLoading = false;

      // Save user data and token in localStorage for persistence
      localStorage.setItem("user", JSON.stringify(user));
      localStorage.setItem("access_token", token);
      localStorage.setItem("role", user.role);
    },

    logout: (state) => {
      state.user = null;
      state.accessToken = null;
      state.refreshToken = null;
      state.isAuthenticated = false;

      // Clear all localStorage items
      localStorage.removeItem("token");
      localStorage.removeItem("access_token");
      localStorage.removeItem("role");
      localStorage.removeItem("user");
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setIsAuthenticated: (state, action: PayloadAction<boolean>) => {
      state.isAuthenticated = action.payload;
    },
  },
});

export const { setUser, logout, setIsAuthenticated, setLoading } = authSlice.actions;
export default authSlice.reducer;
